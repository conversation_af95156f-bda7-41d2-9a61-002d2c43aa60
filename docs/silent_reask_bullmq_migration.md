# SilentReAsk BullMQ 迁移指南

## 概述

已成功将 `SilentReAsk` 类从基于 `DelayedTask` 的定时器实现迁移到基于 BullMQ 的队列实现。新实现保持了完全相同的 API 接口，确保向后兼容性。

## 主要改进

### 1. 更可靠的任务调度
- **持久化**: 任务存储在 Redis 中，服务重启后任务不会丢失
- **分布式**: 支持多个服务实例处理任务
- **监控**: 可以通过 BullMQ 的监控工具查看任务状态

### 2. 更好的性能
- **内存效率**: 不再需要在内存中维护大量的定时器
- **并发处理**: 支持并发处理多个任务
- **资源管理**: 自动清理完成和失败的任务

### 3. 保持原有功能
- **消息检查**: 保留了原有的消息哈希检查逻辑
- **自动重试**: 支持 `auto_retry` 选项
- **任务取消**: 支持 `independent` 选项控制任务取消行为

## API 使用

### ⚠️ 重要变化：需要预注册任务函数

新的实现要求所有任务函数必须预先注册，以解决服务重启后任务丢失的问题。

### 1. 注册任务函数

```typescript
import { SilentReAsk } from '../packages/service/schedule/silent_requestion'

// 注册任务函数
SilentReAsk.registerTask('liveRoomReminder', async (chat_id: string, params?: any) => {
  console.log(`执行直播间提醒任务 for chat: ${chat_id}`, params)
  // 这里放置原来的复杂逻辑
  // 比如：检查用户状态、发送提醒消息等
})

SilentReAsk.registerTask('homeworkCleanup', async (chat_id: string, params?: any) => {
  console.log(`执行作业清理任务 for chat: ${chat_id}`, params)
  // 这里可以放置 Homework1Store.clearUserMessages(chat_id) 等逻辑
})
```

### 2. 调度任务（新的API）

```typescript
// 基本用法
await SilentReAsk.schedule(
  'liveRoomReminder',  // 预注册的任务名
  chatId,              // 聊天ID
  5000,                // 等待时间（毫秒）
  {                    // 任务参数（可选，必须可序列化）
    userId: 'user_123',
    roomId: 'room_456'
  }
)
```

### 3. 带选项的用法

```typescript
// 自动重试任务
await SilentReAsk.schedule(
  'liveRoomReminder',  // 任务名
  chatId,              // 聊天ID
  5000,                // 等待时间
  { userId: 'user_123' }, // 任务参数
  {
    auto_retry: true,    // 有新消息时自动重试
    independent: false   // 取消之前的任务（默认行为）
  }
)

// 独立任务
await SilentReAsk.schedule(
  'homeworkCleanup',   // 任务名
  chatId,              // 聊天ID
  3000,                // 等待时间
  { type: 'homework1' }, // 任务参数
  {
    independent: true    // 不取消之前的任务
  }
)
```

### 4. 迁移示例

```typescript
// ========== 原来的用法 (已废弃) ==========
// await SilentReAsk.schedule(chatId, async () => {
//   // 加锁来判断
//   const lock = new AsyncLock()
//   await lock.acquire(`liveRoomReminder_${chatId}`, async () => {
//     // 如果还在线，退出
//     if ((await ChatStateStore.getFlags<IChattingFlag>(chatId)).is_in_live_room || !await isInClassTime(chatId)) {
//       return
//     }
//     // 原有的复杂逻辑...
//   })
// }, 5 * 60 * 1000) // 5分钟后检查

// ========== 新的用法 ==========
// 1. 首先注册任务（通常在应用启动时）
SilentReAsk.registerTask('liveRoomReminder', async (chat_id: string, params?: any) => {
  // 加锁来判断
  const lock = new AsyncLock()
  await lock.acquire(`liveRoomReminder_${chat_id}`, async () => {
    // 如果还在线，退出
    if ((await ChatStateStore.getFlags<IChattingFlag>(chat_id)).is_in_live_room || !await isInClassTime(chat_id)) {
      return
    }
    // 原有的复杂逻辑...
  })
})

// 2. 然后调度任务
await SilentReAsk.schedule(
  'liveRoomReminder',  // 预注册的任务名
  chatId,              // 聊天ID
  5 * 60 * 1000,       // 5分钟后检查
  {                    // 可选参数
    userId: userId,
    // 其他需要的参数
  }
)
```

## 内部实现变化

### 1. 队列管理
- 使用 BullMQ 的 `Queue` 和 `Worker` 类
- 任务数据序列化存储在 Redis 中
- 支持延迟任务调度

### 2. 任务函数存储
- 由于无法序列化函数，使用内存 Map 存储任务函数
- 每个任务分配唯一的函数名标识符
- 自动清理不再需要的任务函数

### 3. 消息检查逻辑
- 保留原有的 `HashMessagesHandler` 逻辑
- 在任务执行时检查消息哈希变化
- 支持自动重试机制

## 启动和关闭

### 自动启动
Worker 会在第一次调用 `schedule` 时自动启动，无需手动启动。

### 手动管理（可选）
```typescript
// 手动启动 worker
SilentReAsk.startWorker()

// 关闭队列和 worker（通常在应用关闭时调用）
await SilentReAsk.close()
```

## 测试验证

已通过以下测试用例验证功能：

1. ✅ 基本任务执行
2. ✅ 独立任务支持
3. ✅ 任务取消机制
4. ✅ 自动重试功能
5. ✅ 多聊天室支持

## 迁移步骤

⚠️ **注意：新的API不向后兼容，需要手动迁移**

### 1. 注册所有任务函数

在应用启动时，注册所有需要的任务函数：

```typescript
// 在应用启动时注册所有任务
SilentReAsk.registerTask('liveRoomReminder', async (chat_id: string, params?: any) => {
  // 原来的任务逻辑
})

SilentReAsk.registerTask('homeworkCleanup', async (chat_id: string, params?: any) => {
  // 原来的任务逻辑
})

// 启动 worker
SilentReAsk.startWorker()
```

### 2. 更新所有调用点

将所有的 `SilentReAsk.schedule()` 调用更新为新的API：

```typescript
// 原来的调用
// await SilentReAsk.schedule(chatId, taskFunction, waitingTime, options)

// 新的调用
await SilentReAsk.schedule(taskName, chatId, waitingTime, taskParams, options)
```

### 3. 确保 Redis 连接

新实现依赖 Redis，确保 `RedisCacheDB` 正常工作。

### 4. 添加清理逻辑

在应用关闭时调用 `SilentReAsk.close()`：

```typescript
process.on('SIGTERM', async () => {
  await SilentReAsk.close()
  process.exit(0)
})
```

## 性能对比

| 特性 | 原实现 (DelayedTask) | 新实现 (BullMQ) |
|------|---------------------|-----------------|
| 内存使用 | 每个任务占用内存 | 任务存储在 Redis |
| 持久化 | ❌ 服务重启丢失 | ✅ 持久化存储 |
| 分布式 | ❌ 单实例 | ✅ 多实例支持 |
| 监控 | ❌ 无监控 | ✅ BullMQ 监控 |
| 并发 | ❌ 串行执行 | ✅ 并发执行 |

## 注意事项

1. **Redis 依赖**: 新实现完全依赖 Redis，确保 Redis 服务稳定运行
2. **任务注册**: 所有任务函数必须在应用启动时注册，服务重启后会自动恢复
3. **参数序列化**: 任务参数必须是可序列化的数据（JSON），不能包含函数或循环引用
4. **错误处理**: 增强了错误日志记录，便于问题排查
5. **API 变化**: 新API不向后兼容，需要手动迁移所有调用点

## 结论

新的 BullMQ 实现通过预注册任务函数的方式，解决了服务重启后任务丢失的问题，显著提升了系统的可靠性、可扩展性和可维护性。虽然需要手动迁移，但新的架构更加稳定和可维护。

### 迁移检查清单

- [ ] 注册所有需要的任务函数
- [ ] 更新所有 `SilentReAsk.schedule()` 调用
- [ ] 确保任务参数可序列化
- [ ] 测试任务执行和取消逻辑
- [ ] 添加应用关闭时的清理逻辑
- [ ] 验证 Redis 连接正常
