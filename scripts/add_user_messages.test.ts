import { JuziAPI } from '../packages/lib/juzi/api'
import { Config } from '../packages/config'
import { loadConfigByWxId } from '../packages/model/bot_config/load_config'
import { JuZiWecomClient } from '../packages/lib/juzi/client'

interface Customer {
  imContactId: string
  name: string
  avatar: string
  gender?: number
  remark?: string
  createTimestamp: number
  remarkMobiles?: string[]
  imInfo: any
  botInfo: any
  friendshipStatus: number
}

interface CustomerListResponse {
  errcode: number
  errmsg: string
  data: Customer[]
  total: number
  current: number
  pageSize: number
}

/**
 * 获取所有客户列表（自动翻页）
 * @param imBotId 机器人ID
 * @returns 所有客户列表
 */
async function getAllCustomers(imBotId: string): Promise<Customer[]> {
  const client = new JuZiWecomClient()
  const url = 'v2/customer/list'
  const pageSize = 200
  let current = 1
  let allCustomers: Customer[] = []
  let hasMore = true

  console.log('开始获取客户列表...')

  while (hasMore) {
    console.log(`正在获取第 ${current} 页...`)

    const response = await client.get<CustomerListResponse>(url, {
      current,
      pageSize,
      imBotId
    })

    const result = response.data as CustomerListResponse

    if (result.errcode !== 0) {
      throw new Error(`获取客户列表失败: ${result.errmsg}`)
    }

    allCustomers = allCustomers.concat(result.data)
    console.log(`第 ${current} 页获取到 ${result.data.length} 个客户，总计 ${allCustomers.length}/${result.total}`)

    // 检查是否还有更多数据
    hasMore = allCustomers.length < result.total
    current++
  }

  console.log(`获取完成，总共 ${allCustomers.length} 个客户`)
  return allCustomers
}

/**
 * 筛选指定时间之前的客户
 * @param customers 客户列表
 * @param beforeTimestamp 时间戳（毫秒）
 * @returns 筛选后的客户列表
 */
function filterCustomersByTime(customers: Customer[], beforeTimestamp: number): Customer[] {
  return customers.filter((customer) => customer.createTimestamp < beforeTimestamp)
}

describe('Test', function () {
  beforeAll(() => {

  })

  it('should pass', async () => {
    Config.setting.wechatConfig = await loadConfigByWxId('1688855612600530')

    // 2025-05-31 10:03 的时间戳（毫秒）
    const targetDate = new Date('2025-05-31 10:03:00')
    const targetTimestamp = targetDate.getTime()

    console.log(`目标时间: ${targetDate.toLocaleString('zh-CN')} (${targetTimestamp})`)

    try {
      // 获取所有客户
      const allCustomers = await getAllCustomers('1688855612600530')

      // 筛选出指定时间之前的客户
      const filteredCustomers = filterCustomersByTime(allCustomers, targetTimestamp)

      console.log('\n筛选结果:')
      console.log(`总客户数: ${allCustomers.length}`)
      console.log(`2025-05-31 10:03 之前的客户数: ${filteredCustomers.length}`)

      // 按创建时间排序（最新的在前）
      filteredCustomers.sort((a, b) => b.createTimestamp - a.createTimestamp)

      console.log('\n2025-05-31 10:03 之前的客户列表:')

      filteredCustomers.forEach((customer, index) => {
        const createTime = new Date(customer.createTimestamp).toLocaleString('zh-CN')

      })

      // 保存结果到文件（可选）
      // const fs = require('fs')
      // fs.writeFileSync('customers_before_20250531_1003.json', JSON.stringify(filteredCustomers, null, 2))
      // console.log('结果已保存到 customers_before_20250531_1003.json')

    } catch (error) {
      console.error('获取客户列表失败:', error)
    }
  }, 300000) // 增加超时时间到5分钟
})