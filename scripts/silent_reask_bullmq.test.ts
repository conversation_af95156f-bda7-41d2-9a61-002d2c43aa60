import { SilentReAsk } from '../packages/service/schedule/silent_requestion'
import { sleep } from '../packages/lib/schedule/schedule'
import { TaskRegister } from '../apps/yuhe/schedule/register_task'
import { Config } from '../packages/config'
import { loadConfigByWxId } from '../packages/model/bot_config/load_config'
import { TaskName } from '../apps/yuhe/schedule/type'
import { ChatHistoryService } from '../packages/service/chat_history/chat_history'
import { Queue } from 'bullmq'
import { RedisCacheDB } from '../packages/model/redis/redis_cache'
import dayjs from 'dayjs'

describe('SilentReAsk BullMQ Implementation - New API', () => {
  beforeAll(async () => {
    // 注册测试任务
    SilentReAsk.registerTask('testTask', async (chat_id: string, params?: any) => {
      console.log(`Test task executed for chat: ${chat_id}`, params)
      console.log(JSON.stringify(params, null, 4))
    })

    SilentReAsk.registerTask('independentTask1', async (chat_id: string, params?: any) => {
      console.log(`Independent task 1 executed for chat: ${chat_id}`)
      console.log(JSON.stringify(params, null, 4))
    })

    SilentReAsk.registerTask('independentTask2', async (chat_id: string, params?: any) => {
      console.log(`Independent task 2 executed for chat: ${chat_id}`)
      console.log(JSON.stringify(params, null, 4))
    })

    SilentReAsk.registerTask('autoRetryTask', async (chat_id: string, params?: any) => {
      console.log(`Auto retry task executed for chat: ${chat_id}`)
      console.log(JSON.stringify(params, null, 4))
    })

    // 启动 worker
    SilentReAsk.startWorker()
  })

  test('should handle multiple different chat IDs', async () => {
    // 注册测试任务
    SilentReAsk.registerTask('testTask', async (chat_id: string, params?: any) => {
      console.log(`Test task executed for chat: ${chat_id}`, params)
      console.log(JSON.stringify(params, null, 4))
    })

    // 为不同的 chat ID 调度任务
    await SilentReAsk.schedule('testTask', 'chat_a', 1000, {
      param1: 'value1',
      param2: 'value2'
    }, { auto_retry: true, independent: true })

    await SilentReAsk.schedule('independentTask1', 'chat_b', 1200)




    await SilentReAsk.schedule('independentTask2', 'chat_c', 800, {
      chat_id: 'chat_c',
    })

    // 等待所有任务执行
    await sleep(2000)
  }, 10000)

  test('should throw error for unregistered task', async () => {
    const chatId = 'test_chat_5'

    await expect(
      SilentReAsk.schedule('nonExistentTask', chatId, 1000)
    ).rejects.toThrow('Task \'nonExistentTask\' is not registered')
  })
})



describe('SilentReAsk 线上任务', () => {
  beforeAll(async () => {
    Config.setting.wechatConfig = await loadConfigByWxId('1688854546332791')

    TaskRegister.register()
    // 启动 worker
    SilentReAsk.startWorker()
  })

  afterAll(async () => {
  })

  it('测试任务', async () => {
    const chatId = '7881300846030208_1688858335726355'

    // 用户离开直播间超过5分钟，发送提醒
    await SilentReAsk.schedule(TaskName.leave_room, chatId,  1000) // 5分钟后检查

    await sleep(5000)
  }, 1E8)

  it('测试付款任务', async () => {
    const chatId = '7881300846030208_1688858335726355'
    Config.setting.localTest = false

    // 用户离开直播间超过5分钟，发送提醒
    await SilentReAsk.schedule(TaskName.payment_failed, chatId,  1000) // 5分钟后检查

    await sleep(10 * 1000)
  }, 1E8)


  it('测试 auto retry', async () => {
    const chatId = '7881300846030208_1688858335726355'
    Config.setting.localTest = false

    // 用户离开直播间超过5分钟，发送提醒
    await SilentReAsk.schedule(TaskName.test_task, chatId,  3000) // 5分钟后检查
    await ChatHistoryService.addUserMessage(chatId, 'test')

    await sleep(10 * 1000)
  }, 1E8)


  it('测试 independent', async () => {
    const chatId = '7881300846030208_1688858335726355'
    Config.setting.localTest = false

    // 用户离开直播间超过5分钟，发送提醒
    await SilentReAsk.schedule(TaskName.test_task, chatId,  2000, { hi: 'hi' },  { independent: true }) // 5分钟后检查
    await SilentReAsk.schedule(TaskName.test_task, chatId,  2000) // 5分钟后检查

    await sleep(10 * 1000)
  }, 1E8)

  it('查看消息 Worker', async () => {
    const messageQueueBullMQ = new Queue('user-message-queue_1688858335726355', {
      connection: RedisCacheDB.getInstance()
    })

    console.log(JSON.stringify(await messageQueueBullMQ.getWorkers(), null, 4))
  }, 60000)

  it('backoff', async () => {
    const chatId = '7881300846030208_1688858335726355'

    const BACKOFF_SECONDS = [20, 40, 80, 160, 320, 640, 1280, 1800] // 秒
    for (const retryTime of BACKOFF_SECONDS) {
      await SilentReAsk.schedule(
        TaskName.test_task,
        chatId,
        retryTime, // 毫秒
        { retryTime },
        {
          independent: true,
          auto_retry: false
        }
      )
    }

    await sleep(10 * 1000)
  }, 60000)


  it('123123', async () => {
    // console.log()

    console.log(dayjs().subtract(1, 'day').startOf('day').toDate().toLocaleString())
    console.log(dayjs().startOf('day').toDate().toLocaleString())
  }, 60000)
})
