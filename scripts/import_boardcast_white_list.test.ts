import { YuheDataService } from '../apps/yuhe/helper/getter/get_data'
import { PrismaMongoClient } from '../packages/model/mongodb/prisma'
import { YuHeEventHandler } from '../apps/yuhe/client/event_handler'
import { Config } from '../packages/config'
import { loadConfigByWxId } from '../packages/model/bot_config/load_config'

describe('import', () => {
  jest.setTimeout(6000000)
  test('import white list', async() => {
    const courseNo = 20250506
    const mongoClient = PrismaMongoClient.getInstance()
    const chatList = await mongoClient.chat.findMany({ where:{ id:'7881301465908299_1688858335726355', course_no:courseNo } })
    console.log(chatList.filter((chat) => !chat.phone).map((chat) => chat.id).join('\n'))
    for (const chat of chatList.filter((chat) => chat.phone)) {
      if (!chat.phone) continue
      console.log(chat.contact.wx_name, chat.phone)
      await YuheDataService.addBoardcastWhiteList(courseNo, chat.contact.wx_name, chat.phone)
    }
  })

  it('YuHeEventHandler', async () => {
    await new YuHeEventHandler().handleProductPurchase({
      'param': {
        'account': 'visitor_EeBd2r',
        'mobile': '',
        'nickname': 'SYQ',
        'payAmt': '0.01',
        'payMethod': 1,
        'payTime': '2025-05-09 15:43:51',
        'productId': ****************,
        'productName': 'test',
        'roomId': 0,
        'roomName': '',
        'userId': ****************
      },
      'pushTime': *************,
      'roomId': 0,
      'sign': 'cd7858f55f169b6f593db2fe42dd0301',
      'type': 4
    })
  }, 60000)


  it('import white list123', async () => {
    Config.setting.localTest = false

    Config.setting.wechatConfig = await loadConfigByWxId('****************')

    await YuheDataService.importWhiteListOfYesterdayUsers()
  }, 1E8)
})