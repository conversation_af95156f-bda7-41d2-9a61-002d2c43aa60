import { PrismaMongoClient } from '../packages/model/mongodb/prisma'
import axios from 'axios'
import dayjs from 'dayjs'
import { Config } from '../packages/config'
import { loadConfigByWxId } from '../packages/model/bot_config/load_config'
import { YuHeValidator } from '../apps/yuhe/helper/validator/validator'
import { YuheDataService } from '../apps/yuhe/helper/getter/get_data'
import { ChatStateStore } from '../packages/service/local_cache/chat_state_store'
import { IChattingFlag } from '../apps/yuhe/state/user_flags'

describe('Test', function () {
  beforeAll(() => {

  })

  it('添加账号配置', async () => {
    const configs = [
      // 企业账号是可以重叠的
      {
        'enterpriseName': 'yuhe',
        'accountName': 'yuhe3',
        'wechatId': '****************',
        'address': 'http://***************:5002',
        'port': '5002',
        'botUserId': 'SunShengFeng',
        'orgToken': '6801c818f8b463b1d228f23d',
        'enterpriseConfig': {
          'notifyGroupId': 'R:*****************',
        }
      }
    ]

    await PrismaMongoClient.getConfigInstance().config.createMany({
      data: configs
    })


    const res = await axios.post('http://*************:6001/api/clear-server-address-cache')
    console.log(JSON.stringify(res.data, null, 4))

    // YuHe 事件转发缓存清除
  }, 60000)

  it('导入白名单', async () => {
    Config.setting.wechatConfig = await loadConfigByWxId('****************') // 挂个配置，用于发送群通知消息

    // 1. 分组配置，校验直播间配置，进阶课配置
    await YuHeValidator.validateToDayLiveStream()
    await YuHeValidator.validateToDayLiveStreamConfig()

    // 2. 导入白名单
    await YuheDataService.importWhiteListOfYesterdayUsers()
  }, 60000)

  it('ce', async () => {
    const flags = await ChatStateStore.getFlags<IChattingFlag>('7881303126308651_****************')
    console.log(flags.is_in_live_room ?? false)
  }, 60000)


})