import { XingyanAPI } from '../api'

describe('XingyanAPI', () => {
  // 测试参数，请根据实际情况修改
  const testRoomId = '10018130' // 测试直播间ID
  const testMobile = '17664159319' // 测试手机号
  const testUserId = 123456 // 测试用户ID

  it('获取直播间分组信息', async () => {
    const groupInfo = await XingyanAPI.getRoomGroupInfo('系统陪跑班最新期')
    console.log(JSON.stringify(groupInfo, null, 2))
    expect(groupInfo).not.toBeNull()
  }, 30000)

  it('获取直播观看时长', async () => {
    const watchTime = await XingyanAPI.getLiveStreamWatchTime(testRoomId, testMobile)
    console.log('直播观看时长（秒）:', watchTime)
    expect(watchTime).not.toBeNull()
  }, 30000)

  it('获取回放观看时长', async () => {
    const demandTime = await XingyanAPI.getRoomRecordingWatchingTime('********', '***********', undefined)
    console.log('回放观看时长（秒）:', demandTime)
    expect(demandTime).not.toBeNull()
  }, 30000)

  it('获取学员发言记录', async () => {
    const msgPage = await XingyanAPI.getRoomMsgPage('********', undefined, ****************, 1, 10)
    console.log('学员发言记录:', JSON.stringify(msgPage, null, 2))
    expect(msgPage).not.toBeNull()
  }, 30000)

  it('新增白名单用户', async () => {
    const users = [
      {
        account: '***********',
        name: '测试用户10'
      }
    ]
    const result = await XingyanAPI.addImportUser(parseInt(testRoomId, 10), users)
    console.log('新增白名单用户结果:', result)
    expect(result).toBe(true)
  }, 30000)

  it('获取订单列表', async () => {
    const orderPage = await XingyanAPI.getOrderInfoPage(testMobile)
    console.log('订单列表:', JSON.stringify(orderPage, null, 2))
    expect(orderPage).not.toBeNull()
    if (orderPage && orderPage.list && orderPage.list.length > 0) {
      console.log('第一个订单信息:', JSON.stringify(orderPage.list[0], null, 2))
    }
  }, 30000)

  it('获取白名单用户列表', async () => {
    const whitelist = await XingyanAPI.getImportUserPage(parseInt(testRoomId, 10))
    console.log('白名单用户列表:', JSON.stringify(whitelist, null, 2))
    expect(whitelist).not.toBeNull()

    // 测试根据手机号查询
    const whitelistByPhone = await XingyanAPI.getImportUserPage(parseInt(testRoomId, 10), '***********')
    console.log('根据手机号查询白名单:', JSON.stringify(whitelistByPhone, null, 2))
    expect(whitelistByPhone).not.toBeNull()

    // 测试数据结构
    if (whitelist && whitelist.length > 0) {
      const firstUser = whitelist[0]
      expect(firstUser).toHaveProperty('account')
      expect(firstUser).toHaveProperty('name')
    }
  }, 30000)


  it('获取所有白名单用户', async () => {
    const l = await XingyanAPI.getAllWhiteListUsers(********)
    console.log(l.length)

    console.log(JSON.stringify(l, null, 4))
  }, 60000)
})
