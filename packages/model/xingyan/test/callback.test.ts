import { XingyanCallback } from '../callback'
import { XingyanPushType } from '../type'

describe('XingyanCallback', () => {
  it('解析学员进入房间回调', () => {
    const callbackData = {
      type: XingyanPushType.USER_ENTER_ROOM,
      param: {
        userId: **********,
        openId: 'openid123',
        unionId: 'unionid123',
        mobile: '***********',
        headImgUrl: 'https://example.com/avatar.jpg',
        nickname: '测试用户',
        roomId: 10001,
        loginTime: '2024-10-30 12:34:56',
        channelId: 1000007,
        channelName: '测试渠道',
        ip: '***********',
        browserType: 'WX',
        account: 'test_account'
      },
      sign: '01decc556bde5f30f85589f31d156648',
      pushTime: *************,
      roomId: 10001
    }

    const result = XingyanCallback.handleUserEnterRoom(callbackData)
    expect(result).toEqual(callbackData.param)
  })

  it('解析学员离开房间回调', () => {
    const callbackData = {
      type: XingyanPushType.USER_LEAVE_ROOM,
      param: {
        userId: **********,
        roomId: 10001,
        logoutTime: '2024-10-30 13:34:56',
        watchTime: 360,
        nickname: '测试用户',
        account: 'test_account',
        mobile: '***********'
      },
      sign: '01decc556bde5f30f85589f31d156648',
      pushTime: *************,
      roomId: 10001
    }

    const result = XingyanCallback.handleUserLeaveRoom(callbackData)
    expect(result).toEqual(callbackData.param)
  })

  it('解析商品点击回调', () => {
    const callbackData = {
      type: XingyanPushType.PRODUCT_CLICK,
      param: {
        roomId: ********,
        productId: *************,
        userId: **********,
        nickname: '测试用户',
        account: 'test_account',
        mobile: '***********'
      },
      sign: '01decc556bde5f30f85589f31d156648',
      pushTime: *************,
      roomId: 10001
    }

    const result = XingyanCallback.handleProductClick(callbackData)
    expect(result).toEqual(callbackData.param)
  })

  it('解析商品购买回调', () => {
    const callbackData = {
      type: XingyanPushType.PRODUCT_PURCHASE,
      param: {
        roomId: ********,
        roomName: '测试直播间',
        productId: *************,
        productName: '测试商品',
        userId: **********,
        payMethod: 1,
        payAmt: '100.99',
        payTime: '2024-10-30 01:02:03',
        nickname: '测试用户',
        account: 'test_account',
        mobile: '***********',
        orderPhone: '***********'
      },
      sign: '01decc556bde5f30f85589f31d156648',
      pushTime: *************,
      roomId: 10001
    }

    const result = XingyanCallback.handleProductPurchase(callbackData)
    expect(result).toEqual(callbackData.param)
  })

  it('解析关闭订单回调', () => {
    const callbackData = {
      type: XingyanPushType.ORDER_CLOSE,
      param: {
        roomId: ********,
        roomName: '测试直播间',
        productId: *************,
        productName: '测试商品',
        userId: **********,
        payMethod: 1,
        payAmt: '100.99',
        cancelTime: '2024-10-30 01:02:03',
        nickname: '测试用户',
        account: 'test_account',
        mobile: '***********'
      },
      sign: '01decc556bde5f30f85589f31d156648',
      pushTime: *************,
      roomId: 10001
    }

    const result = XingyanCallback.handleOrderClose(callbackData)
    expect(result).toEqual(callbackData.param)
  })

  it('根据类型处理不同回调', () => {
    const callbackData = {
      type: XingyanPushType.USER_ENTER_ROOM,
      param: {
        userId: **********,
        openId: 'openid123',
        unionId: 'unionid123',
        mobile: '***********',
        headImgUrl: 'https://example.com/avatar.jpg',
        nickname: '测试用户',
        roomId: 10001,
        loginTime: '2024-10-30 12:34:56',
        channelId: 1000007,
        channelName: '测试渠道',
        ip: '***********',
        browserType: 'WX',
        account: 'test_account'
      },
      sign: '01decc556bde5f30f85589f31d156648',
      pushTime: *************,
      roomId: 10001
    }

    const result = XingyanCallback.handleCallback(callbackData)
    expect(result).toEqual(callbackData.param)
  })

  it('处理不支持的回调类型', () => {
    const callbackData = {
      type: 999, // 不支持的类型
      param: {},
      sign: '01decc556bde5f30f85589f31d156648',
      pushTime: *************,
      roomId: 10001
    }

    expect(() => {
      XingyanCallback.handleCallback(callbackData)
    }).toThrow('不支持的回调类型: 999')
  })
})
