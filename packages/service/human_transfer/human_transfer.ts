import { Chat, ChatDB } from '../database/chat'
import { JuziAPI } from '../../lib/juzi/api'
import { Config } from '../../config'
import { ChatStateStore } from '../local_cache/chat_state_store'
import logger from '../../model/logger/logger'
import { GroupNotification } from '../group_notification/group_notification'

export class HumanTransfer {
  /**
 * 转交人工，toBot为true时，表示转交机器人
 * @param chatId
 * @param userId
 * @param notifyMessage
 * @param toHuman
  */
  public static async transfer(chatId: string, userId: string, notifyMessage: string, toHuman: boolean |'onlyNotify' = true) {
    if (typeof toHuman === 'boolean' && await ChatDB.getById(chatId)) {
      await ChatDB.setHumanInvolvement(chatId, toHuman)
    } else {
      if (!await ChatDB.getById(chatId)) {
        const currentSender = await JuziAPI.getCustomerInfo(Config.setting.wechatConfig?.id as string, userId)
        await ChatDB.create({
          id: chatId,
          round_ids: [],
          contact: {
            wx_id: userId,
            wx_name: currentSender ? currentSender.name : userId,
          },
          wx_id: Config.setting.wechatConfig?.id as string,
          created_at: new Date(),
          chat_state: await ChatStateStore.get(chatId)
        })
      }
    }

    // 包装通知
    const chat = await ChatDB.getById(chatId) as Chat
    let contactName = userId
    if (chat && chat.contact && chat.contact.wx_name) {
      contactName = chat.contact.wx_name
    }

    let notificationMessage: string

    notificationMessage = `${contactName} ${notifyMessage}`

    if (toHuman === true) {
      notificationMessage += '\n[心碎]AI已关闭[心碎]'
    }

    logger.log({ chat_id: chatId }, '通知人工接入：', notificationMessage)

    if (toHuman === 'onlyNotify' && Config.setting.localTest) {
      return
    }

    await GroupNotification.notify(notificationMessage) // 群通知
  }
}