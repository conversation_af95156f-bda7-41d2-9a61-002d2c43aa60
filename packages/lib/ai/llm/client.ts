import { AzureChatOpenAI, ChatOpenAI } from '@langchain/openai'
import { ChatAlibabaTongyi } from '@langchain/community/chat_models/alibaba_tongyi'
import { Config } from '../../../config'


interface IOpenAIInitParams {
    model?: string
    temperature?: number
    max_tokens?: number
    top_p?: number
    frequency_penalty?: number
    timeout?: number
}

export const CLIENT_DEFAULT_TIMEOUT  = 2 * 60 * 1000 // 2 minutes

export class OpenAIClient {
  public static getClient(params: IOpenAIInitParams = {}): ChatOpenAI {
    const {
      model = 'gpt-4.1',
      temperature = 0,
      max_tokens = 1000,
      top_p = 0.9,
      frequency_penalty = 0.8,
      timeout = CLIENT_DEFAULT_TIMEOUT
    } = params

    if (timeout < 1000) {
      throw new Error('Timeout 以秒为单位，请不要填写太短，否则请求会全部超时')
    }

    return new ChatOpenAI({
      model: model,
      temperature: temperature,
      maxTokens: max_tokens,
      topP: top_p,
      frequencyPenalty: frequency_penalty,
      timeout: timeout,
      openAIApiKey: Config.setting.openai.apiKeys[0],
      configuration: { baseURL: Config.setting.openai.apiBaseUrl },
      maxRetries: 1
    })
  }
}

export class AzureOpenAIClient {
  public static getClient(params: IOpenAIInitParams = {}): ChatOpenAI {
    const {
      model = 'gpt-4.1',
      temperature = 0,
      max_tokens = 1000,
      top_p = 0.9,
      frequency_penalty = 0.5,
      timeout = CLIENT_DEFAULT_TIMEOUT
    } = params

    if (timeout < 1000) {
      throw new Error('Timeout 以秒为单位，请不要填写太短，否则请求会全部超时')
    }


    let selectedModel = 'gpt-4.1' // Azure 部署名称
    if (model.includes('gpt-4.1-mini')) {
      selectedModel = 'gpt-4.1-mini'
    } else if (model.includes('o3-mini')) {
      selectedModel = 'o3-mini'
    }

    const azureConfig: any = {
      temperature: temperature,
      maxTokens: max_tokens,
      topP: top_p,
      model: selectedModel,
      frequencyPenalty: frequency_penalty,
      timeout: timeout,
      azureOpenAIApiDeploymentName: selectedModel,
      azureOpenAIApiKey: Config.setting.azureOpenAI.azureOpenAIApiKey,
      azureOpenAIApiVersion: Config.setting.azureOpenAI.azureOpenAIApiVersion,
      azureOpenAIApiInstanceName: Config.setting.azureOpenAI.azureOpenAIApiInstanceName,
      configuration: { baseURL: Config.setting.azureOpenAI.apiBaseUrl },
      maxRetries: 1
    }

    if (azureConfig.azureOpenAIApiDeploymentName === 'o3-mini') {
      delete azureConfig.maxTokens
      delete azureConfig.temperature
      delete azureConfig.topP
      delete azureConfig. frequencyPenalty
      azureConfig.model_kwargs = { 'max_completion_tokens': max_tokens }
    }

    return new AzureChatOpenAI(azureConfig)
  }
}

export class StableClaude {
  public static getClient(model = 'claude-3-5-sonnet-20241022', temperature = 0): ChatOpenAI {
    return new ChatOpenAI({
      openAIApiKey: Config.setting.stableClaude.apiKey,
      configuration: {
        baseURL: Config.setting.stableClaude.apiBaseUrl
      },
      model: model,
      temperature: temperature,
      timeout: CLIENT_DEFAULT_TIMEOUT,
      maxRetries: 1
    })
  }
}


export class CheapOpenAI {
  public static getClient(model = 'gpt-4.1', temperature = 0): ChatOpenAI {
    return new ChatOpenAI({
      openAIApiKey: Config.setting.cheapOpenAI.apiKey,
      configuration: {
        baseURL: Config.setting.cheapOpenAI.apiBaseUrl
      },
      model: model,
      temperature: temperature,
      timeout: CLIENT_DEFAULT_TIMEOUT,
      maxRetries: 1
    })
  }
}


export class MiTaAI {
  public static getClient(model : 'concise' | 'detail' | 'research', temperature = 0): ChatOpenAI {
    return new ChatOpenAI({
      openAIApiKey: Config.setting.cheapOpenAI.apiKey,
      configuration: {
        baseURL: 'http://112.124.32.162:8000/v1',
        defaultHeaders: {
          Authorization: 'Bearer 61638845b28fa859c374a79f-0abc662597fb4d4ca498a786cbffb761'
        }
      },
      modelName: model,
      temperature: temperature,
      timeout: CLIENT_DEFAULT_TIMEOUT // 1.5 minutes
    })
  }
}

export class QwenMax {
  public static getClient(temperature = 0) {
    return new ChatAlibabaTongyi({
      alibabaApiKey: Config.setting.qwen.apiKey,
      temperature: temperature,
      model: 'qwen-max',
    })
  }
}

export class PerplexityAI {
  public static getClient(temperature = 0) {
    return new ChatOpenAI({
      openAIApiKey: '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',
      configuration: {
        baseURL: 'https://api.perplexity.ai'
      },
      modelName: 'llama-3-sonar-large-32k-online',
      temperature: temperature,
      timeout: CLIENT_DEFAULT_TIMEOUT // 1.5 minutes
    })
  }
}

export class QwenVideoAnalyzer {
  public static getClient(temperature = 0.7) {
    return new ChatAlibabaTongyi({
      alibabaApiKey: Config.setting.qwen.apiKey,
      temperature: temperature,
      model: 'qwen-2.5-omni',
      maxTokens: 1000,
      topP: 0.8,
    })
  }
}