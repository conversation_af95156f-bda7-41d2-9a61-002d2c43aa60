import { EventTracker, IEventType } from '../../../packages/model/logger/data_driven'
import { ObjectUtil } from '../../../packages/lib/object'
import { HumanTransfer } from '../../../packages/service/human_transfer/human_transfer'
import logger from '../../../packages/model/logger/logger'

export enum YuHeHumanTransferType {
  UnknownMessageType = 1,
  NotBindPhone = 2,
  JoinedGroup = 3,
  FailedToJoinGroup = 4,
  ProcessImage = 5,
  MessageSendFailed = 6,
  ConfirmedAddress = 7,
  HesitatePayment = 8,
  LogOutNotify = 9,
  RefundCourse = 10,
  SendMiniProgram = 11,
  SoftwareIssue = 12,
  RobotDetected = 13,
  PaidCourse = 14,
  RefusePurchase = 15,
  WeeklyCard = 16,
  RepeatedWealthOrchardAnalyze = 17,
  ComplaintOrRefundOrChangeTeachingAssistant = 18,
  Course188Consulting = 21,
  ExceededSalesNodeInvokeCountLimit = 22,
  ExplicitlyPurchases = 23,
  ProcessVideo = 24
}


/**
 * 宇和项目转人工处理
 */
export class YuHeHumanTransfer {
  /**
   * 转交人工，toBot为true时，表示转交机器人
   * @param chatId
   * @param userId
   * @param transferType
   * @param toHuman
   * @param additionMsg
   */
  public static async transfer(chatId: string, userId: string, transferType: YuHeHumanTransferType, toHuman: boolean | 'onlyNotify' = true, additionMsg?: string) {
    if (userId === 'null') {
      logger.error('[YuHeHumanTransfer] userId is null', transferType)
      return
    }

    if (transferType !== YuHeHumanTransferType.UnknownMessageType)  { // 因为图片，文件等转人工的 日志在上级进行处理，这里不进行重复处理
      EventTracker.track(chatId, IEventType.TransferToManual, { reason: ObjectUtil.enumValueToKey(YuHeHumanTransferType, transferType) })
    }

    // 拼接要发送的消息
    // 通知类型
    const notificationMessages = {
      [YuHeHumanTransferType.UnknownMessageType]: '客户发了一个文件，无法处理，请及时介入处理',
      [YuHeHumanTransferType.ProcessImage]: '客户发了一张【图片】，请人工观察',
      [YuHeHumanTransferType.ProcessVideo]: '客户发了一个【视频】，请人工观察',
      [YuHeHumanTransferType.NotBindPhone]: '客户手机号绑定失败，请人工处理',
      [YuHeHumanTransferType.SoftwareIssue]: '用户软件或者课程链接出问题，请人工处理',
      [YuHeHumanTransferType.MessageSendFailed]: '消息发送失败，请人工处理',
      [YuHeHumanTransferType.RobotDetected]: '用户识别到了AI，请人工处理',
      [YuHeHumanTransferType.ConfirmedAddress]: '用户付款后已发送地址，请人工确认',
      [YuHeHumanTransferType.HesitatePayment] : '用户付款失败，请人工确认',
      [YuHeHumanTransferType.LogOutNotify]: '客户直播掉线，请处理',
      [YuHeHumanTransferType.RefundCourse]: '客户之前购买过课程，请人工进行退款',
      [YuHeHumanTransferType.SendMiniProgram]: '已发送小程序给客户，请人工观察',
      [YuHeHumanTransferType.PaidCourse]: '[烟花]客户已付款[烟花]',
      [YuHeHumanTransferType.RefusePurchase]: '客户拒绝购买',
      [YuHeHumanTransferType.WeeklyCard]: '客户索要周卡，请人工发放',
      [YuHeHumanTransferType.RepeatedWealthOrchardAnalyze]: '客户已经分析过财富果园，可能存在重复解读，请人工观察',
      [YuHeHumanTransferType.ComplaintOrRefundOrChangeTeachingAssistant]:'客户投诉了，请人工处理',
      [YuHeHumanTransferType.Course188Consulting]: '客户回复了188课程，请人工处理',
      [YuHeHumanTransferType.ExceededSalesNodeInvokeCountLimit]:'客户销售阶段聊天过多，请人工处理',
      [YuHeHumanTransferType.FailedToJoinGroup]: '客户拉群失败，请人工处理',
      [YuHeHumanTransferType.ExplicitlyPurchases]: '客户准备购买课程，请人工观察'
    }

    let message = notificationMessages[transferType] as string
    if (additionMsg) {
      message += `\n${additionMsg}`
    }

    return await HumanTransfer.transfer(chatId, userId, message, toHuman)
  }
}