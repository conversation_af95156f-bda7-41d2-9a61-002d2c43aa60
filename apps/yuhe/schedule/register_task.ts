import { SilentReAsk } from '../../../packages/service/schedule/silent_requestion'
import { AsyncLock } from '../../../packages/model/lock/lock'
import { ChatStateStore } from '../../../packages/service/local_cache/chat_state_store'
import { IChattingFlag } from '../state/user_flags'
import dayjs from 'dayjs'
import RateLimiter from '../../../packages/model/redis/rate_limiter'
import { YuheDataService } from '../helper/getter/get_data'
import { getState } from '../../../packages/service/llm/state'
import { YuHeContextManager } from '../workflow/context/context_manager'
import { YuHeReply } from '../workflow/actions/reply'
import { firstAskIntention, isInClassTime } from '../client/event_handler'
import { WecomMessageSender } from '../../../packages/service/message_handler/juzi/message_sender'
import { EventTracker, IEventType } from '../../../packages/model/logger/data_driven'
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>rans<PERSON>, YuHeHumanTransferType } from '../human_transfer/human_transfer'
import { getUserId } from '../../../packages/config/chat_id'
import { sendMsg } from '../../../packages/service/visualized_sop/visualized_sop_processor'
import { ChatHistoryService } from '../../../packages/service/chat_history/chat_history'
import { Homework1Store } from '../workflow/nodes/homework1'
import { Homework2Store } from '../workflow/nodes/homework2'
import { TaskName } from './type'
import logger from '../../../packages/model/logger/logger'
import { GroupNotification } from '../../../packages/service/group_notification/group_notification'


export class TaskRegister {
  public static register() {
    SilentReAsk.registerTask(TaskName.leave_room, async (chat_id, params) => {
      const user_id = getUserId(chat_id)

      // 加锁来判断
      const lock = new AsyncLock()
      await lock.acquire(`liveRoomReminder_${chat_id}`, async () => {
        // 如果还在线，退出
        if ((await ChatStateStore.getFlags<IChattingFlag>(chat_id)).is_in_live_room || !await isInClassTime(chat_id)) {
          return
        }
        // 如果没上课30分钟，就不发
        if (dayjs().isBefore(dayjs().hour(19).minute(20))) {
          return
        }

        const limiter = new RateLimiter({ // 30 分钟内只允许一次解读
          windowSize: 30 * 60,
          maxRequests: 1
        })
        const currentTime = await YuheDataService.getCurrentTime(chat_id)
        const nodeName = `leave_room_notify_day${currentTime.day}`

        const isAllowed = await limiter.isAllowed(nodeName, chat_id)

        if (!isAllowed) {
          return
        }

        await ChatStateStore.increaseNodeCount(chat_id, nodeName)
        const nodeInvokeCount = await ChatStateStore.getNodeCount(chat_id, nodeName)
        const maxNotifyCount = 2
        if (nodeInvokeCount > maxNotifyCount) {
          return
        }

        const state = await getState(chat_id, user_id)
        const context = await YuHeContextManager.build({
          state,
          chatHistoryRounds: 2,
          dynamicPrompt: '你需要提醒用户他掉线了，直播课程很重要，赶快进来看中神通老师的直播课程',
        })

        await YuHeReply.invoke({
          state,
          temperature: 0.8,
          max_tokens: 100,
          promptName: 'leave_room_notify',
          context: context,
          noSplit:true
        })
      })
    })


    SilentReAsk.registerTask(TaskName.payment_failed, async (chat_id: string, params) => {
      const userId = getUserId(chat_id)
      // 加锁来判断
      const lock = new AsyncLock()
      await lock.acquire('orderFailureCheck', async () => {
        // 如果已经处理过下单失败，退出
        if ((await ChatStateStore.getFlags<IChattingFlag>(chat_id)).handled_failed_payment) {
          return
        }

        // 检查是否付款
        if (await YuheDataService.isPaidSystemCourse(chat_id)) {
          return
        }

        // 通知用户
        await WecomMessageSender.sendById({
          chat_id: chat_id,
          user_id: userId,
          ai_msg: '亲，老师这边看您刚才点击了商品，但似乎没有完成支付。您是在操作过程中遇到了什么问题吗？需要我帮您解答吗？'
        })

        // 通知人工客服
        EventTracker.track(chat_id, IEventType.PaymentNotCompleted)
        await YuHeHumanTransfer.transfer(chat_id, userId, YuHeHumanTransferType.HesitatePayment, 'onlyNotify')

        // 标记为已处理下单失败
        await ChatStateStore.update(chat_id, {
          state: {
            handled_failed_payment: true
          }
        })
      })
    })

    // 注册作业清理任务
    SilentReAsk.registerTask(TaskName.homework_cleanup, async (chat_id: string, params) => {
      const { storeType } = params || {}
      if (storeType === 'homework1') {
        Homework1Store.clearUserMessages(chat_id)
      } else if (storeType === 'homework2') {
        Homework2Store.clearUserMessages(chat_id)
      }
    })


    // 注册手机号 backoff 检查任务
    SilentReAsk.registerTask(TaskName.phone_backoff_check, async (chat_id: string, params) => {
      const retryTime = params?.retryTime

      const phoneNumber = await YuheDataService.bindPhoneFromRemark(chat_id)
      if (phoneNumber) {
        const flags = await ChatStateStore.getFlags<IChattingFlag>(chat_id)

        if (!flags.is_bind_phone) {
          await GroupNotification.notify(`手机号在 ${retryTime} 秒后被自动绑定，手机号为 ${phoneNumber}`)

          await ChatStateStore.update(chat_id, {
            state: {
              is_bind_phone: true
            }
          })
        }
      }
    })

    // 注册手机号检查任务
    SilentReAsk.registerTask(TaskName.phone_check, async (chat_id: string, params) => {
      const userId = getUserId(chat_id)

      const phoneNumber = await YuheDataService.bindPhoneFromRemark(chat_id)
      if (!phoneNumber) {
        await YuHeHumanTransfer.transfer(chat_id, userId, YuHeHumanTransferType.NotBindPhone, 'onlyNotify')
      }
    })

    // 注册询问意图任务
    SilentReAsk.registerTask(TaskName.ask_intention, async (chat_id: string, params) => {
      const userId = getUserId(chat_id)
      await firstAskIntention(chat_id, userId)
    })

    // 注册询问意图提醒任务
    SilentReAsk.registerTask(TaskName.ask_intention_reminder, async (chat_id: string, params) => {
      const userId = getUserId(chat_id)

      const isUserReply = await ChatHistoryService.isLastMessageWithDuration(chat_id, 59, 'minute')
      if (isUserReply) return

      await sendMsg(userId, chat_id, ['同学，你忙完了吗？看你一个小时没回复我了。麻烦抽空填写下我发给你的信息表，这样老师可以更好地帮你诊断问题哦～'], '要求用户填写信息表')
    })

    // 注册催促发送抖音截图任务
    SilentReAsk.registerTask(TaskName.urge_douyin_screenshot, async (chat_id: string, params) => {
      const userId = getUserId(chat_id)

      if (await YuheDataService.isDoingDouyin(chat_id)) {
        await sendMsg(userId, chat_id, '同学你抖音截图先发我一下，我看看你做成什么样了？', '第一天催促客户发送抖音截图')
      }

      // 再调度引导课程任务
      await SilentReAsk.schedule(
        TaskName.guide_lesson,
        chat_id,
        1000 * 60 * 10, // 10分钟后执行，给抖音截图任务留出时间
        undefined,
        { auto_retry: true, independent:true }
      )
    })

    // 注册引导课程任务
    SilentReAsk.registerTask(TaskName.guide_lesson, async (chat_id: string, params) => {
      const userId = getUserId(chat_id)

      const userName = await YuheDataService.getUserName(chat_id)
      await sendMsg(userId, chat_id, `${userName}，今天你所说出来的问题明天晚上18：50 中神通老师都会讲到，
咱们也不用过于担心，只要咱们认真学习，肯定是可以解决掉的，咱们就放宽心，跟着老师学习就好了`)
    })


    SilentReAsk.registerTask(TaskName.test_task, async (chat_id: string, params) => {
      logger.log(`执行测试任务 for chat: ${chat_id}`, params)
    })
  }
}