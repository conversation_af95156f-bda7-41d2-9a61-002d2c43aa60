import { YuheDataService } from '../../getter/get_data'
import { MetaActionHandler } from '../metaActionHandler'


describe('metaActionTest', () => {
  it('testGetMetaAction', async () => {
    YuheDataService.getCurrentTime = async () => {
      return {
        day: 3,
        time: '18:38:00',
      }
    }
    const { thinkPrompt, metaActions } = await MetaActionHandler.getThinkAndMetaActions('7881299950922845_1688858213716953')
    console.log(thinkPrompt)
    console.log(metaActions)
  }, 9e8)
})