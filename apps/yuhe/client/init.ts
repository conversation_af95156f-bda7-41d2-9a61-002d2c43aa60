import { loadConfigByAccountName } from '../../../packages/service/database/config'
import chalk from 'chalk'
import { Config } from '../../../packages/config'
import { ProjectName, workflowConfig } from '../../registry/registry'
import { YuheVisualizedSopProcessor } from '../visualized_sop/yuhe_visualized_sop_processor'
import { TaskRegister } from '../schedule/register_task'
import { SilentReAsk } from '../../../packages/service/schedule/silent_requestion'

export async function initConfig() {
  // 环境配置
  const env = process.env.NODE_ENV
  if (!env) {
    console.error('请设置环境变量 NODE_ENV')
    process.exit(1)
  }

  Config.setting.startTime = Date.now()
  Config.setting.BOT_NAME = workflowConfig[ProjectName.YuHe].avatarName
  Config.setting.enterpriseName = workflowConfig[ProjectName.YuHe].name

  // 注入配置
  // 读取注入的 姓名
  const name = process.env.WECHAT_NAME
  if (!name) {
    console.error('请设置环境变量 WECHAT_NAME')
    process.exit(1)
  }

  Config.setting.wechatConfig = await loadConfigByAccountName(name)

  console.log(
    chalk.green(
      `当前账号：${chalk.bold(Config.setting.wechatConfig?.name)}`,
    ),
  )

  // 注册所有任务函数
  TaskRegister.register()

  // 启动 SilentReAsk Worker
  SilentReAsk.startWorker()

  // SOP Worker
  new YuheVisualizedSopProcessor().start()
}