import { FreeTalkNode } from './nodes/freeTalk'
import { IWorkflowState } from '../../../packages/service/llm/state'
import { YuheWorkFlowNode } from './nodes/baseNode'
import { LLM } from '../../../packages/lib/ai/llm/LLM'
import { ChatStateStore } from '../../../packages/service/local_cache/chat_state_store'
import logger from '../../../packages/model/logger/logger'
import { XMLHelper } from '../../../packages/lib/xml/xml'
import { PromptTemplate } from '@langchain/core/prompts'
import { SendFileNode } from './nodes/sendFile'
import { IntentionQueryNode } from './nodes/intentionQuery'
import { DouyinAnalysisNode } from './nodes/douyinAnalysis'
import { Homework1Node } from './nodes/homework1'
import { Homework2Node } from './nodes/homework2'
import { YuHeNode } from './nodes/types'
import { PhoneQueryNode } from './nodes/phoneQuery'
import { <PERSON><PERSON><PERSON><PERSON>umanTransfer, YuHeHumanTransferType } from '../human_transfer/human_transfer'
import { EventTracker, IEventType } from '../../../packages/model/logger/data_driven'
import { YuHeEventHandler } from '../client/event_handler'

export const YuHeNodeMap: { [key in YuHeNode]?: typeof YuheWorkFlowNode } = {
  [YuHeNode.FreeTalk]: FreeTalkNode,
  [YuHeNode.SendFile]: SendFileNode,
  [YuHeNode.IntentionQuery]: IntentionQueryNode,
  [YuHeNode.DouyinAnalysis]: DouyinAnalysisNode,
  [YuHeNode.Homework1]: Homework1Node,
  [YuHeNode.Homework2]: Homework2Node,
  [YuHeNode.PhoneQuery]: PhoneQueryNode
}

export class NodeRouter {
  /**
   * 根据用户消息进行路由，特别注意这里的路由要写的 特定情况才能跳转，不能太通用，不然容易路由到错误的节点
   * 返回 End, 表示不执行任何节点逻辑
   * @param state
   */
  public static async route(state: IWorkflowState): Promise<YuHeNode> {
    if (!state.userMessage) return YuHeNode.Dummy

    // 废话内容过滤
    const isChatter = this.filterChatter(state.userMessage)
    if (isChatter) return YuHeNode.DummyEnd

    // 客户识别AI检查
    const isRobotDetection = await this.checkRobotDetection(state.chat_id, state.round_id, state.user_id, state.userMessage)
    if (isRobotDetection) return YuHeNode.DummyEnd

    // 账号截图诊断
    const isDouyinImage = this.checkDouyinImage(state.userMessage)
    if (isDouyinImage) return YuHeNode.DouyinAnalysis

    // 索要完课礼检查
    const isRewardAsking = this.checkRewardAsking(state.userMessage)
    if (isRewardAsking) return YuHeNode.SendFile

    // 第一天作业打卡
    const isHomework1 = this.checkHomework1(state.userMessage)
    if (isHomework1) return YuHeNode.Homework1

    // 第二天作业打卡
    const isHomework2 = this.checkHomework2(state.userMessage)
    if (isHomework2) return YuHeNode.Homework2

    // 支付成功检查
    const isPaymentImage = this.checkPaymentImage(state.userMessage)
    if (isPaymentImage) {
      await ChatStateStore.update(state.chat_id, { state: { is_complete_payment: true } })
      EventTracker.track(state.chat_id, IEventType.PaymentComplete)
      try {
        await YuHeEventHandler.inviteToGroup(state.chat_id, state.user_id)
        await YuHeHumanTransfer.transfer(state.chat_id, state.user_id, YuHeHumanTransferType.PaidCourse, 'onlyNotify')
      } catch (e) {
        logger.error('拉群失败', e)
        // 通知进群失败
        await YuHeHumanTransfer.transfer(state.chat_id, state.user_id, YuHeHumanTransferType.FailedToJoinGroup, true)
      }
    }

    // 意图分类路由
    return await this.routeByCategory(state.userMessage, state.chat_id, state.round_id)
  }

  // 废话内容过滤
  static filterChatter(userMessage: string): boolean {
    const KEYWORDS = ['好的', 'OK', '收到', '谢谢', '感谢', '晚安', '谢谢老师', '嗯', '嗯嗯', '[OK]', '行', '嗯呢', '好', '👌', '[握手]', '好的，谢谢', '1']
    return KEYWORDS.some((keyword) => userMessage.toLowerCase() === keyword.toLowerCase())
  }

  // 客户识别AI检查
  public static async checkRobotDetection(chat_id: string, round_id: string, user_id: string, userMessage: string): Promise<boolean> {
    const robotDetectionNodeName = 'RobotDetected'
    const AI_KEYWORDS = ['人工', '机器人', '是真人', 'ai', '人机', '智能', '智慧体']
    const isRobotKeywordPresent = AI_KEYWORDS.some((keyword) => userMessage.toLowerCase().includes(keyword))

    if (isRobotKeywordPresent) {
      // LLM二次校验AI检查
      const robotDetectionPromptTemplate = PromptTemplate.fromTemplate(`# 检查AI识别
- 根据客户最近发言，判断其是否怀疑你是AI或者机器人，若怀疑则输出true，反之输出false
- 先输出理由到 <reason></reason> 标签中，并将结果true或者false输出到 <answer></answer> 标签中

## 输出示例
<reason>客户说他之前做过AI，并没有怀疑你是AI</reason>
<answer>false</answer>

## 客户输入
${userMessage}

开始输出`, { templateFormat: 'mustache' })
      const res = await LLM.predict(robotDetectionPromptTemplate, { meta: { promptName: 'robot_detection', chat_id: chat_id, round_id: round_id } }, {})
      const robotDetectionResult = XMLHelper.extractContent(res, 'answer')
      if (robotDetectionResult != 'true') {
        return false
      }

      await ChatStateStore.increaseNodeCount(chat_id, robotDetectionNodeName)
      const robotDetectedNum = await ChatStateStore.getNodeCount(chat_id, robotDetectionNodeName)

      if (robotDetectedNum >= 2) {
        try {
          await YuHeHumanTransfer.transfer(chat_id, user_id, YuHeHumanTransferType.RobotDetected, true, `客户说：${userMessage}`)
          await ChatStateStore.update(chat_id, { nodeInvokeCount: { [robotDetectionNodeName]: 0 } })
          return true
        } catch (error) {
          logger.error('用户识别到AI，但转人工失败:', error)
        }
      } else if (robotDetectedNum === 1) {
        await YuHeHumanTransfer.transfer(chat_id, user_id, YuHeHumanTransferType.RobotDetected, 'onlyNotify', `客户说：${userMessage}`)
      }
    }
    return false
  }

  static checkDouyinImage(userMessage: string): boolean {
    if (['不是抖音首页', '并非抖音首页', '【普通图片】'].some((item) => userMessage.includes(item))) {
      return false
    }
    return userMessage.includes('【抖音首页截图】')
  }

  static checkRewardAsking(userMessage: string): boolean {
    const KEYWORDS = ['完课礼', '定位', '指南', '爆单', '祝福', '好评礼']
    return userMessage.split('\n').some((subItem) => KEYWORDS.includes(subItem.trim()))
  }

  static checkHomework1(userMessage: string): boolean {
    return ['商业定位', '内容定位', '人设定位', '你想一个月通过抖音赚多少钱'].some((item) => userMessage.includes(item))
  }

  static checkHomework2(userMessage: string): boolean {
    return ['你的身份', '你打算做什么', '提出具体要求'].some((item) => userMessage.includes(item))
  }

  static checkPaymentImage(userMessage: string): boolean {
    return userMessage.includes('图片') &&
         userMessage.includes('宇合传媒') &&
         userMessage.includes('支付成功') &&
         ['2980', '500', '1000'].some((amount) => userMessage.includes(amount))
  }

  // 意图分类路由
  private static async routeByCategory(userMessage: string, chat_id: string, round_id: string): Promise<YuHeNode> {
    const category = await NodeRouter.classify(userMessage, chat_id, round_id)

    // const currentTime = await DataService.getCurrentTime(chat_id)

    if (category === 1) {
      return YuHeNode.SendFile
    } else if (category === 2) {
      return YuHeNode.DouyinAnalysis
    } else if (category === 3) {
      return YuHeNode.Homework1
    } else if (category === 4) {
      return YuHeNode.Homework2
    } else if (category === 5) {
      return YuHeNode.DummyEnd
    }
    return YuHeNode.Dummy
  }

  public static async classify(userMessage: string, chat_id: string, round_id: string) {
    const routerPromptTemplate = PromptTemplate.fromTemplate(`# 意图分类
根据以下指令，分析客户的输入，然后将其分类到以下节点之一，并返回对应的节点编号

1. 发送课程资料：客户输入为索要完课礼/课程回放/好评礼/作业或其他课程相关资料时，判断前请不要进行过度推理
例如：“老师要完课礼”“有回放吗”“已好评[强]”“客户发送某平台对课程商品好评的图片”“昨天留的什么作业呀”，客户单独发送要完课礼的暗号“定位”“指南”“爆单”“祝福”

2. 账号截图诊断: 客户发送自己的抖音首页截图，包括账号头像，账号名，背景图，获赞，关注，粉丝，作品等信息
例如：【抖音首页截图】这是一张抖音首页截图，账号头像是侧身展示手臂肌肉的健身照片，账号名称为“南昌经济技术开发区思迈健身房”，背景图是健身房外部招牌。该账号获赞1648，关注1281人，粉丝287人。页面显示该健身房位于南昌市，有联系方式、地址和营业时间信息，目前共发布了68个作品，从展示来看内容主要包括课程教学、器械训练和学员锻炼等，每个作品的获赞数在十几到二十几个不等

3. 第一课作业：客户输入包含商业定位，内容定位，人设定位，目标客户，你想一个月通过抖音赚多少钱这几项内容时，分类到这个节点
注意：如果客户发送姓名称呼，个人实体店/连锁店，行业类目，年营业额，是否抖音在做，所在城市等相关内容时，不属于这个节点

4. 第二课作业：客户输入同时包含“客户的身份”“客户打算做什么”“提出具体要求”相关内容时，分类到这个节点
例如：我是在上海开健身房的，我想通过短视频吸引更多客户到店，请根据目前抖音比较火的同类视频，帮我创作5条能吸引同城客户的方案

5. 异常问题处理：我们的产品是冥想课程，当用户表示投诉、退款或更换导师需求时进入此节点
例如：投诉相关("你们的产品是骗人的"、"我要投诉")、退款相关("我要退款/退费/退课")、导师变更("我要换助教"、"能换个助教吗")
注意：非课程投诉的投诉不属此节点；仅询问是否可以退课不属此节点；态度不强烈的不属于此节点

0. 正常对话聊天: 不符合任何上述条件的对话

请根据上述指令进行分类，先输出理由到 <reason></reason> 标签中，并将结果输出到 <answer></answer> 标签中 (仅包含节点编号或 "null")

## 输出示例
<reason>用户询问上课地点, 没有表示出明确出购买意向</reason>
<answer>0</answer>

客户输入
{{userMessage}}
开始输出`, { templateFormat: 'mustache' })
    const res = await LLM.predict(routerPromptTemplate, { meta: { promptName: 'classify_category', chat_id: chat_id, round_id: round_id } }, { userMessage: userMessage })

    const categoryNumber = XMLHelper.extractContent(res, 'answer')
    if (!categoryNumber) return 0
    return parseInt(categoryNumber, 10)
  }
}