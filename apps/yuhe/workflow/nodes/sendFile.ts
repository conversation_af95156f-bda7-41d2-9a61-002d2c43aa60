import { trackInvoke, <PERSON><PERSON><PERSON><PERSON><PERSON>FlowNode } from './baseNode'
import { IWorkflowState } from '../../../../packages/service/llm/state'
import { ChatStateStore } from '../../../../packages/service/local_cache/chat_state_store'
import { YuHeNode } from './types'
import { ChatHistoryService } from '../../../../packages/service/chat_history/chat_history'
import { AIMessage, HumanMessage, SystemMessage } from '@langchain/core/messages'
import { LLM } from '../../../../packages/lib/ai/llm/LLM'
import { DateHelper } from '../../../../packages/lib/date/date'
import { XMLHelper } from '../../../../packages/lib/xml/xml'
import logger from '../../../../packages/model/logger/logger'
import { IScheduleTime, YuheDataService } from '../../helper/getter/get_data'
import { WecomMessageSender } from '../../../../packages/service/message_handler/juzi/message_sender'
import { ISendMedia } from '../../../../packages/service/visualized_sop/visualized_sop_type'
import { IWecomMsgType } from '../../../../packages/lib/juzi/type'
import { sendMsg } from '../../../../packages/service/visualized_sop/visualized_sop_processor'
import { FreeTalkNode } from './freeTalk'
import { Config } from '../../../../packages/config'
import { IChattingFlag } from '../../state/user_flags'

enum FileType {
  NodeInvoke = 0,
  Course1Reward = 1,
  Course2Reward = 2,
  Course3Reward = 3,
  Course4Reward = 4,
  Course1Backup = 5,
  Course2Backup = 6,
  Course3Backup = 7,
  Course4Backup = 8,
  Course1Homework = 9,
  Course2Homework = 10,
  Course3Homework = 11,
  GoodReviewGift = 12,
}

export class SendFileNode extends YuheWorkFlowNode {
  private static fileNameList = [
    '其他资料',
    '第一课完课礼',
    '第二课完课礼',
    '第三课完课礼',
    '第四课完课礼',
    '第一课回放',
    '第二课回放',
    '第三课回放',
    '第四课回放',
    '第一课作业',
    '第二课作业',
    '第三课作业',
    '好评礼',
  ]
  private static fileCase: { [key: string]: FileType } = {
    '其他资料': FileType.NodeInvoke,
    '第一课完课礼': FileType.Course1Reward,
    '第二课完课礼': FileType.Course2Reward,
    '第三课完课礼': FileType.Course3Reward,
    '第四课完课礼': FileType.Course4Reward,
    '第一课回放': FileType.Course1Backup,
    '第二课回放': FileType.Course2Backup,
    '第三课回放': FileType.Course3Backup,
    '第四课回放': FileType.Course4Backup,
    '第一课作业': FileType.Course1Homework,
    '第二课作业': FileType.Course2Homework,
    '第三课作业': FileType.Course3Homework,
    '好评礼': FileType.GoodReviewGift,
  }
  @trackInvoke
  public static async invoke(state: IWorkflowState) {
    const fileName = await this.classifier(state)
    const currentTime = await YuheDataService.getCurrentTime(state.chat_id)
    const fileNames: string[] = fileName.includes(',') ? fileName.split(',') : [fileName]
    logger.log(`fileNames to be sending: ${fileNames}`)

    for (const name of fileNames) {
      logger.log(`fileName to be sending: ${name}`)
      const fileType = await this.getFileType(state.chat_id, name, currentTime)
      logger.log(`fileType to be sending: ${fileType}`)

      switch (fileType) {
        case FileType.NodeInvoke:
          return await FreeTalkNode.invoke(state)  // 转为调用之前要调用的节点

        case FileType.Course1Reward: {
          const flags = await ChatStateStore.getFlags<IChattingFlag>(state.chat_id)
          await ChatStateStore.update(state.chat_id, { state:<IChattingFlag>{ ...flags, is_send_completion_course_gift_day1:true } })
          await sendMsg(state.user_id, state.chat_id, [<ISendMedia>{ description: name, msg: { type: IWecomMsgType.File, name: name,
            url: 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/yuhe/sop/%E3%80%90Day1%E3%80%91%E5%AE%8C%E8%AF%BE%E7%A4%BC-%E5%95%86%E4%B8%9A%E5%AE%9A%E4%BD%8D%E6%A8%A1%E6%9D%BF.pdf'
          } }], name, true)
          break
        }

        case FileType.Course2Reward: {
          const flags = await ChatStateStore.getFlags<IChattingFlag>(state.chat_id)
          await ChatStateStore.update(state.chat_id, { state:<IChattingFlag>{ ...flags, is_send_completion_course_gift_day2:true } })
          await sendMsg(state.user_id, state.chat_id, [<ISendMedia>{ description: name, msg: { type: IWecomMsgType.File, name: name,
            url: 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/yuhe/sop/%E3%80%90Day2%E3%80%91%E5%AE%8C%E8%AF%BE%E7%A4%BC-%E5%AE%9E%E4%BD%93%E5%95%86%E5%AE%B6%E8%BF%90%E8%90%A5%E5%A4%A7%E5%85%A8.pdf'
          } }], name, true)
          break
        }

        case FileType.Course3Reward: {
          const flags = await ChatStateStore.getFlags<IChattingFlag>(state.chat_id)
          await ChatStateStore.update(state.chat_id, { state:<IChattingFlag>{ ...flags, is_send_completion_course_gift_day3:true } })
          await sendMsg(state.user_id, state.chat_id, [<ISendMedia>{ description: name, msg: { type: IWecomMsgType.File, name: name,
            url: 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/yuhe/sop/%E3%80%90Day3%E3%80%91%E5%AE%8C%E8%AF%BE%E7%A4%BC-%E5%AE%9E%E4%BD%93%E5%95%86%E5%AE%B6DeepSeek%E5%AE%9E%E6%88%98%E6%8C%87%E5%8D%97.pdf'
          } }], name, true)
          break
        }

        case FileType.Course4Reward: {
          const flags = await ChatStateStore.getFlags<IChattingFlag>(state.chat_id)
          await ChatStateStore.update(state.chat_id, { state:<IChattingFlag>{ ...flags, is_send_completion_course_gift_day4:true } })
          await sendMsg(state.user_id, state.chat_id, [<ISendMedia>{ description: name, msg: { type: IWecomMsgType.File, name: name,
            url: 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/yuhe/sop/%E3%80%90Day4%E3%80%91%E5%AE%8C%E8%AF%BE%E7%A4%BC-%E5%95%86%E5%AE%B6%E7%99%BE%E9%97%AE%E7%99%BE%E7%AD%94.pdf'
          } }], name, true)
          break
        }

        case FileType.Course1Backup:
        { const courseBackup = await YuheDataService.getCourseLink(state.chat_id, 1) ?? ''
          const flags = await ChatStateStore.getFlags<IChattingFlag>(state.chat_id)
          await ChatStateStore.update(state.chat_id, { state:<IChattingFlag>{ ...flags, is_send_course_replay_day1:true } })
          await WecomMessageSender.sendById({ user_id: state.user_id, chat_id: state.chat_id, ai_msg: `第一课回放链接：${courseBackup}` }, { round_id: state.round_id })
          break }

        case FileType.Course2Backup:
        { const courseBackup = await YuheDataService.getCourseLink(state.chat_id, 2) ?? ''
          const flags = await ChatStateStore.getFlags<IChattingFlag>(state.chat_id)
          await ChatStateStore.update(state.chat_id, { state:<IChattingFlag>{ ...flags, is_send_course_replay_day2:true } })
          await WecomMessageSender.sendById({ user_id: state.user_id, chat_id: state.chat_id, ai_msg: `第二课回放链接：${courseBackup}` }, { round_id: state.round_id })
          break }

        case FileType.Course3Backup:
        { const courseBackup = await YuheDataService.getCourseLink(state.chat_id, 3) ?? ''
          const flags = await ChatStateStore.getFlags<IChattingFlag>(state.chat_id)
          await ChatStateStore.update(state.chat_id, { state:<IChattingFlag>{ ...flags, is_send_course_replay_day3:true } })
          await WecomMessageSender.sendById({ user_id: state.user_id, chat_id: state.chat_id, ai_msg: `第三课回放链接：${courseBackup}` }, { round_id: state.round_id })
          break }

        case FileType.Course4Backup:
        { const courseBackup = await YuheDataService.getCourseLink(state.chat_id, 4) ?? ''
          const flags = await ChatStateStore.getFlags<IChattingFlag>(state.chat_id)
          await ChatStateStore.update(state.chat_id, { state:<IChattingFlag>{ ...flags, is_send_course_replay_day4:true } })
          await WecomMessageSender.sendById({ user_id: state.user_id, chat_id: state.chat_id, ai_msg: `第四课回放链接：${courseBackup}` }, { round_id: state.round_id })
          break }

        case FileType.Course1Homework:
          await WecomMessageSender.sendById({ user_id: state.user_id, chat_id: state.chat_id, ai_msg: `【第一课作业】
商业定位：
内容定位：
人设定位：
目标客户：
你想一个月通过抖音赚多少钱：
【用老师发的模版填写，收到后我会1对1给您反馈】` }, { round_id: state.round_id })
          break

        case FileType.Course2Homework:
          await WecomMessageSender.sendById({ user_id: state.user_id, chat_id: state.chat_id, ai_msg: `【第二课作业】
填写以下内容：

身份（你是在哪里做XXXX的）：
你打算做什么（我想要用XXXX做XXXXXX）：
提出具体要求（我想要你帮我做XXX）：

【按照以上模板在里面填写，老师会给你一对一诊断】` }, { round_id: state.round_id })
          break

        case FileType.Course3Homework:
          await WecomMessageSender.sendById({ user_id: state.user_id, chat_id: state.chat_id, ai_msg: `【第三课作业】

任务：开通四大主流平台账号【抖音、快手、视频号、快手】
设置好账号名字、头像、个人介绍

发给助教老师检查
【按照以上要求做，老师会给你一对一诊断】` }, { round_id: state.round_id })
          break

        case FileType.GoodReviewGift:
          await WecomMessageSender.sendById({ user_id: state.user_id, chat_id: state.chat_id, ai_msg: '🎁双周卡会员点击链接领取：\nhttps://m.kaogujia.com/webview/receiveMembership?aid=4305\n领取后下载考古加app，微信授权登录就可以了' }, { round_id: state.round_id })
          await sendMsg(state.user_id, state.chat_id, [<ISendMedia>{ description: name, msg: {
            type: IWecomMsgType.File,
            name: name,
            url: 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/yuhe/sop/%E3%80%90%E5%A5%BD%E8%AF%84%E7%A4%BC%E3%80%91DeepSeek%2015%E5%A4%A9%E6%8C%87%E5%AF%BC%E2%BC%BF%E5%86%8C%E2%B8%BA%E4%BB%8E%E2%BC%8A%E2%BB%94%E5%88%B0%E7%B2%BE%E9%80%9A.pdf'
          } }], name, true)
          break

        default:
          await FreeTalkNode.invoke(state)
          return (await ChatStateStore.get(state.chat_id)).nextStage as YuHeNode
      }
    }
    return (await ChatStateStore.get(state.chat_id)).nextStage as YuHeNode
  }

  private static async getFileType(chatId:string, name: string, currentTime: IScheduleTime): Promise<FileType> {
    if (!this.fileNameList.includes(name)) {
      return FileType.NodeInvoke
    }

    if (!['回放', '完课礼'].some((keyword) => name.includes(keyword))) {
      return this.fileCase[name]
    }

    const isBeforeTime = (targetTime: string, targetDay: number): boolean => {
      return currentTime.day < targetDay || (DateHelper.isTimeBefore(currentTime.time, targetTime) && currentTime.day === targetDay)
    }

    const flagMapping: { [key: string]: IChattingFlag } = {
      '第一课回放': { is_ask_day1_replay: true },
      '第二课回放': { is_ask_day2_replay: true },
      '第三课回放': { is_ask_day3_replay: true },
      '第四课回放': { is_ask_day4_replay: true },
    }

    const typeMapping: { [key: string]: FileType } = {
      '第一课回放': isBeforeTime('21:42:00', 1) ? FileType.NodeInvoke : FileType.Course1Backup,
      '第二课回放': isBeforeTime('22:52:00', 2) ? FileType.NodeInvoke : FileType.Course2Backup,
      '第三课回放': isBeforeTime('22:18:00', 3) ? FileType.NodeInvoke : FileType.Course3Backup,
      '第四课回放': isBeforeTime('22:00:00', 4) ? FileType.NodeInvoke : FileType.Course4Backup,
      '第一课完课礼': isBeforeTime('21:22:00', 1) ? FileType.NodeInvoke : FileType.Course1Reward,
      '第二课完课礼': isBeforeTime('22:32:00', 2) ? FileType.NodeInvoke : FileType.Course2Reward,
      '第三课完课礼': isBeforeTime('21:58:00', 3) ? FileType.NodeInvoke : FileType.Course3Reward,
      '第四课完课礼': isBeforeTime('21:40:00', 4) ? FileType.NodeInvoke : FileType.Course4Reward,
    }

    // 更新 ChatStateStore
    if (flagMapping[name]) {
      await ChatStateStore.update(chatId, { state: flagMapping[name] as IChattingFlag })
    }

    return typeMapping[name] ?? FileType.NodeInvoke
  }

  private static async classifier(state: IWorkflowState) {
    const chatHistory = await ChatHistoryService.getLLMChatHistory(state.chat_id, 2)
    const formattedChatHistory = ChatHistoryService.formatHistoryHelper(chatHistory)
    const fileNameListStr = this.fileNameList.join('，')
    const currentTime = await YuheDataService.getCurrentTime(state.chat_id)
    let todayCourse = ''
    if (currentTime) {
      if (currentTime.day == 0) { todayCourse = '明日第一课' }
      else if (currentTime.day < 5) { todayCourse = `今日课程：第${currentTime.day}课` }
      else { todayCourse = '速成班课程已结束' }
    }
    const res = await LLM.predictMessage([
      new SystemMessage(`# 发送文件
- 请根据当前时间和近两轮与客户交互中的信息，判断客户需要的资料，并从以下列表中选择对应的资料输出：【${fileNameListStr}】，如果没有找到对应资料，就输出【其他资料】

## 模糊匹配：
- 奖励（完课礼）
- 定位（第一课完课礼）
- 指南（第二课完课礼）
- 爆单（第三课完课礼）
- 祝福（第四课完课礼）

## 课程信息：
- 第一课：18:50到21:42
- 第二课：18:50到22:52
- 第三课：18:50到22:18
- 第四课：18:50到22:00

## 输出要求：
- 根据当前时间，客户的今日课程与对话记录综合分析客户需要的资料，可以同时输出多个资料，中间用“,”隔开
- 请主要关注客户的需求，而不是${Config.setting.BOT_NAME}说的内容，历史已经发送过的资料不要重复发送
- 若客户发送关键口令：定位，指南，爆单，祝福，则无论历史是否发过该资料，都要重新发送
- 若客户发送关于课程评价的图片，大概率是索要好评礼
- 请不要胡乱判断资料，找不到对应资料，或者找到资料了不满足时间等条件，都输出【其他资料】
- 先将分析过程输出到 <reason></reason> 标签中，例如：<reason>因当前时间是22:20:00，今日课程为第一课，用户在对话中表示要奖励，应该发送【第一课完课礼】</reason>
- 仅将资料内容输出到 <answer></answer> 标签中，例如：<answer>第一课完课礼</answer>`),
      new HumanMessage(`当前时间：09:59:41
今日课程：第2课
对话记录：
客户: 老师发一下回放吧`),
      new AIMessage(`<reason>因当前时间为09:59:41，今日课程为第二课，但客户请求“老师发一下回放吧”，结合时间判断第二课尚未开始，因此客户索要的应该是第一课的回放，故选择【第一课回放】</reason>
<answer>第一课回放</answer>`),
      //       new HumanMessage(`当前时间：2025-01-08 周三 11:20:26
      // 对话记录：
      // 客户: 收到谢谢
      // 麦子老师: 😊`),
      //       new AIMessage('<reason></reason> <answer></answer>'),
      //       new HumanMessage(`当前时间：2025-01-09 周四 15:16:00
      // 对话记录：
      // 客户: 好的`),
      //       new AIMessage('<reason></reason> <answer></answer>'),
      new HumanMessage(`当前时间：${currentTime.time}
${todayCourse}
对话记录：\n${formattedChatHistory}`),
    ], { meta: { chat_id: state.chat_id, round_id: state.round_id, promptName: 'sendfile_category' } })
    const fileName = XMLHelper.extractContent(res, 'answer')
    if (!fileName) {
      return '其他资料'
    }
    return fileName
  }
}